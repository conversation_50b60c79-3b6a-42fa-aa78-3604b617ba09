/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    
    /* Neutrals */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition: all 0.2s ease-in-out;
    --transition-fast: all 0.15s ease-in-out;
    --transition-slow: all 0.3s ease-in-out;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--white);
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

/* Header */
.header {
    background: var(--white);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: var(--transition);
}

.navbar {
    padding: var(--spacing-4) 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-logo h1 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-8);
    margin: 0;
}

.nav-link {
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-auth {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    position: relative;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    object-fit: cover;
}

.user-name {
    font-weight: 500;
    color: var(--gray-700);
}

.dropdown {
    position: relative;
}

.dropdown-btn {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: var(--radius);
    transition: var(--transition);
}

.dropdown-btn:hover {
    background: var(--gray-100);
}

.dropdown-content {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    padding: var(--spacing-2);
    display: none;
    z-index: 1000;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.dropdown-content a {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    text-decoration: none;
    color: var(--gray-700);
    border-radius: var(--radius);
    transition: var(--transition);
}

.dropdown-content a:hover {
    background: var(--gray-50);
    color: var(--primary-color);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--gray-600);
    border-radius: var(--radius-full);
    transition: var(--transition);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

.btn-large {
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
}

.btn-full {
    width: 100%;
}

/* Hero Section */
.hero {
    padding: calc(80px + var(--spacing-20)) 0 var(--spacing-20);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.hero-content {
    max-width: 500px;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--gray-900);
    line-height: 1.1;
    margin-bottom: var(--spacing-6);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
    line-height: 1.5;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-mockup {
    width: 300px;
    height: 400px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.hero-mockup::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.hero-icon {
    font-size: 4rem;
    color: var(--white);
    z-index: 1;
}

/* Sections */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.section-subtitle {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Features Section */
.features {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-8);
}

.feature-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-8);
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transform: scaleX(0);
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.feature-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.feature-description {
    color: var(--gray-600);
    margin-bottom: var(--spacing-6);
    line-height: 1.6;
}

.feature-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-1);
    transition: var(--transition);
}

.feature-link:hover {
    color: var(--primary-dark);
    gap: var(--spacing-2);
}

/* Templates Preview Section */
.templates-preview {
    padding: var(--spacing-20) 0;
    background: var(--gray-50);
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-12);
}

.template-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.template-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.template-preview {
    height: 200px;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4);
}

.template-mockup {
    width: 120px;
    height: 160px;
    background: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-2);
    position: relative;
}

.template-mockup.modern {
    border-left: 4px solid var(--primary-color);
}

.template-mockup.classic {
    border-top: 4px solid var(--accent-color);
}

.template-mockup.creative {
    border-left: 4px solid var(--success-color);
    border-top: 4px solid var(--success-color);
}

.mockup-header {
    height: 20px;
    background: var(--gray-200);
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-2);
}

.mockup-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
}

.mockup-line {
    height: 8px;
    background: var(--gray-300);
    border-radius: var(--radius-sm);
}

.mockup-line.short {
    width: 60%;
}

.template-card h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: var(--spacing-4) var(--spacing-4) var(--spacing-2);
}

.template-card p {
    color: var(--gray-600);
    margin: 0 var(--spacing-4) var(--spacing-4);
    font-size: var(--font-size-sm);
}

.template-card .btn {
    margin: 0 var(--spacing-4) var(--spacing-4);
}

.templates-cta {
    text-align: center;
}

/* Pricing Section */
.pricing {
    padding: var(--spacing-20) 0;
    background: var(--white);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-8);
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-8);
    text-align: center;
    position: relative;
    transition: var(--transition);
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.pricing-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.pricing-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.pricing-price {
    margin-bottom: var(--spacing-6);
}

.price {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--gray-900);
}

.period {
    color: var(--gray-500);
    font-size: var(--font-size-base);
}

.pricing-features {
    list-style: none;
    margin-bottom: var(--spacing-8);
}

.pricing-features li {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) 0;
    color: var(--gray-600);
}

.pricing-features .fa-check {
    color: var(--success-color);
}

.pricing-features .fa-times {
    color: var(--gray-400);
}

/* Footer */
.footer {
    background: var(--gray-900);
    color: var(--gray-300);
    padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-8);
}

.footer-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--spacing-4);
}

.footer-subtitle {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--white);
    margin-bottom: var(--spacing-4);
}

.footer-description {
    color: var(--gray-400);
    margin-bottom: var(--spacing-4);
    line-height: 1.6;
}

.footer-social {
    display: flex;
    gap: var(--spacing-3);
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    color: var(--gray-400);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--white);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-2);
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--white);
}

.footer-contact {
    color: var(--gray-400);
    line-height: 1.8;
}

.footer-contact i {
    color: var(--primary-color);
    margin-right: var(--spacing-2);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: var(--spacing-8);
    text-align: center;
    color: var(--gray-500);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: var(--white);
    margin: 5% auto;
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    width: 90%;
    max-width: 400px;
    position: relative;
    box-shadow: var(--shadow-xl);
}

.close {
    position: absolute;
    right: var(--spacing-4);
    top: var(--spacing-4);
    color: var(--gray-400);
    font-size: var(--font-size-2xl);
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
}

.close:hover {
    color: var(--gray-600);
}

.auth-form h2 {
    text-align: center;
    margin-bottom: var(--spacing-6);
    color: var(--gray-900);
}

.form-group {
    margin-bottom: var(--spacing-4);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-1);
    font-weight: 500;
    color: var(--gray-700);
}

.form-group input {
    width: 100%;
    padding: var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    transition: var(--transition);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.auth-switch {
    text-align: center;
    margin-top: var(--spacing-4);
    color: var(--gray-600);
}

.auth-switch a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.auth-switch a:hover {
    text-decoration: underline;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-5 { margin-top: var(--spacing-5); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }

.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-5 { margin-bottom: var(--spacing-5); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }

.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-5 { padding: var(--spacing-5); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }
