// Supabase Configuration
const SUPABASE_URL = 'https://unyoxxghnsllcpaxplvk.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVueW94eGdobnNsbGNwYXhwbHZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwNTMzMzYsImV4cCI6MjA2NDYyOTMzNn0.qusFWwjMQTewCESyMSLhIXEijJJ4yE_Fn_eMwmNe5eo';

// Initialize Supabase client
const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Database helper functions
class DatabaseService {
    constructor() {
        this.supabase = supabase;
    }

    // Authentication methods
    async signUp(email, password, fullName) {
        try {
            const { data, error } = await this.supabase.auth.signUp({
                email,
                password,
                options: {
                    data: {
                        full_name: fullName
                    }
                }
            });

            if (error) throw error;

            // Create profile
            if (data.user) {
                await this.createProfile(data.user.id, email, fullName);
            }

            return { data, error: null };
        } catch (error) {
            console.error('Sign up error:', error);
            return { data: null, error };
        }
    }

    async signIn(email, password) {
        try {
            const { data, error } = await this.supabase.auth.signInWithPassword({
                email,
                password
            });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Sign in error:', error);
            return { data: null, error };
        }
    }

    async signOut() {
        try {
            const { error } = await this.supabase.auth.signOut();
            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Sign out error:', error);
            return { error };
        }
    }

    async getCurrentUser() {
        try {
            const { data: { user }, error } = await this.supabase.auth.getUser();
            if (error) throw error;
            return { user, error: null };
        } catch (error) {
            console.error('Get user error:', error);
            return { user: null, error };
        }
    }

    // Profile methods
    async createProfile(userId, email, fullName) {
        try {
            const { data, error } = await this.supabase
                .from('profiles')
                .insert([
                    {
                        id: userId,
                        email,
                        full_name: fullName,
                        avatar_url: null
                    }
                ]);

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Create profile error:', error);
            return { data: null, error };
        }
    }

    async getProfile(userId) {
        try {
            const { data, error } = await this.supabase
                .from('profiles')
                .select('*')
                .eq('id', userId)
                .single();

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get profile error:', error);
            return { data: null, error };
        }
    }

    async updateProfile(userId, updates) {
        try {
            const { data, error } = await this.supabase
                .from('profiles')
                .update(updates)
                .eq('id', userId);

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Update profile error:', error);
            return { data: null, error };
        }
    }

    // CV methods
    async createCV(userId, title, templateId, cvData) {
        try {
            const { data, error } = await this.supabase
                .from('cvs')
                .insert([
                    {
                        user_id: userId,
                        title,
                        template_id: templateId,
                        cv_data: cvData,
                        is_public: false
                    }
                ])
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Create CV error:', error);
            return { data: null, error };
        }
    }

    async getUserCVs(userId) {
        try {
            const { data, error } = await this.supabase
                .from('cvs')
                .select(`
                    *,
                    cv_templates (
                        name,
                        description,
                        preview_image
                    )
                `)
                .eq('user_id', userId)
                .order('updated_at', { ascending: false });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get user CVs error:', error);
            return { data: null, error };
        }
    }

    async getCV(cvId) {
        try {
            const { data, error } = await this.supabase
                .from('cvs')
                .select(`
                    *,
                    cv_templates (
                        name,
                        description,
                        template_data
                    )
                `)
                .eq('id', cvId)
                .single();

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get CV error:', error);
            return { data: null, error };
        }
    }

    async updateCV(cvId, updates) {
        try {
            const { data, error } = await this.supabase
                .from('cvs')
                .update({
                    ...updates,
                    updated_at: new Date().toISOString()
                })
                .eq('id', cvId)
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Update CV error:', error);
            return { data: null, error };
        }
    }

    async deleteCV(cvId) {
        try {
            const { error } = await this.supabase
                .from('cvs')
                .delete()
                .eq('id', cvId);

            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Delete CV error:', error);
            return { error };
        }
    }

    // Template methods
    async getTemplates() {
        try {
            const { data, error } = await this.supabase
                .from('cv_templates')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get templates error:', error);
            return { data: null, error };
        }
    }

    async getTemplate(templateId) {
        try {
            const { data, error } = await this.supabase
                .from('cv_templates')
                .select('*')
                .eq('id', templateId)
                .single();

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get template error:', error);
            return { data: null, error };
        }
    }

    // QR Code methods
    async createQRCode(userId, cvId, qrData, title) {
        try {
            const { data, error } = await this.supabase
                .from('qr_codes')
                .insert([
                    {
                        user_id: userId,
                        cv_id: cvId,
                        qr_data: qrData,
                        title: title || 'CV QR Code'
                    }
                ])
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Create QR code error:', error);
            return { data: null, error };
        }
    }

    async getUserQRCodes(userId) {
        try {
            const { data, error } = await this.supabase
                .from('qr_codes')
                .select(`
                    *,
                    cvs (
                        title
                    )
                `)
                .eq('user_id', userId)
                .order('created_at', { ascending: false });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get user QR codes error:', error);
            return { data: null, error };
        }
    }

    async deleteQRCode(qrCodeId) {
        try {
            const { error } = await this.supabase
                .from('qr_codes')
                .delete()
                .eq('id', qrCodeId);

            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Delete QR code error:', error);
            return { error };
        }
    }

    // NFC Card methods
    async createNFCCard(userId, cvId, cardName, nfcData) {
        try {
            const { data, error } = await this.supabase
                .from('nfc_cards')
                .insert([
                    {
                        user_id: userId,
                        cv_id: cvId,
                        card_name: cardName,
                        nfc_data: nfcData,
                        is_active: true
                    }
                ])
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Create NFC card error:', error);
            return { data: null, error };
        }
    }

    async getUserNFCCards(userId) {
        try {
            const { data, error } = await this.supabase
                .from('nfc_cards')
                .select(`
                    *,
                    cvs (
                        title
                    )
                `)
                .eq('user_id', userId)
                .order('created_at', { ascending: false });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get user NFC cards error:', error);
            return { data: null, error };
        }
    }

    async updateNFCCard(cardId, updates) {
        try {
            const { data, error } = await this.supabase
                .from('nfc_cards')
                .update({
                    ...updates,
                    updated_at: new Date().toISOString()
                })
                .eq('id', cardId)
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Update NFC card error:', error);
            return { data: null, error };
        }
    }

    async deleteNFCCard(cardId) {
        try {
            const { error } = await this.supabase
                .from('nfc_cards')
                .delete()
                .eq('id', cardId);

            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Delete NFC card error:', error);
            return { error };
        }
    }

    // Real-time subscriptions
    subscribeToUserCVs(userId, callback) {
        return this.supabase
            .channel('user-cvs')
            .on('postgres_changes', 
                { 
                    event: '*', 
                    schema: 'public', 
                    table: 'cvs',
                    filter: `user_id=eq.${userId}`
                }, 
                callback
            )
            .subscribe();
    }

    subscribeToUserQRCodes(userId, callback) {
        return this.supabase
            .channel('user-qr-codes')
            .on('postgres_changes', 
                { 
                    event: '*', 
                    schema: 'public', 
                    table: 'qr_codes',
                    filter: `user_id=eq.${userId}`
                }, 
                callback
            )
            .subscribe();
    }

    subscribeToUserNFCCards(userId, callback) {
        return this.supabase
            .channel('user-nfc-cards')
            .on('postgres_changes', 
                { 
                    event: '*', 
                    schema: 'public', 
                    table: 'nfc_cards',
                    filter: `user_id=eq.${userId}`
                }, 
                callback
            )
            .subscribe();
    }

    // Unsubscribe from channel
    unsubscribe(subscription) {
        if (subscription) {
            this.supabase.removeChannel(subscription);
        }
    }
}

// Create global database service instance
window.dbService = new DatabaseService();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DatabaseService, supabase };
}
