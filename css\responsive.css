/* Responsive Design */

/* Large screens (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1200px;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .section-title {
        font-size: 3rem;
    }
}

/* Medium screens (768px to 1199px) */
@media (max-width: 1199px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-12);
    }
    
    .hero-content {
        max-width: 600px;
        margin: 0 auto;
    }
    
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .pricing-grid {
        grid-template-columns: 1fr;
        max-width: 400px;
    }
    
    .pricing-card.featured {
        transform: none;
        order: -1;
    }
}

/* Small screens (768px and down) */
@media (max-width: 768px) {
    /* Navigation */
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-4) 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-list {
        flex-direction: column;
        gap: var(--spacing-4);
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(7px) rotate(45deg);
    }
    
    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-7px) rotate(-45deg);
    }
    
    .nav-auth {
        flex-direction: column;
        gap: var(--spacing-2);
        width: 100%;
        margin-top: var(--spacing-4);
    }
    
    .nav-auth .btn {
        width: 100%;
        max-width: 200px;
        margin: 0 auto;
    }
    
    /* Hero Section */
    .hero {
        padding: calc(80px + var(--spacing-12)) 0 var(--spacing-12);
        min-height: auto;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
        margin-bottom: var(--spacing-4);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-6);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-3);
    }
    
    .hero-buttons .btn {
        width: 100%;
        max-width: 280px;
    }
    
    .hero-mockup {
        width: 250px;
        height: 320px;
    }
    
    .hero-icon {
        font-size: 3rem;
    }
    
    /* Sections */
    .section-title {
        font-size: var(--font-size-3xl);
    }
    
    .section-subtitle {
        font-size: var(--font-size-lg);
    }
    
    /* Features */
    .features {
        padding: var(--spacing-16) 0;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }
    
    .feature-card {
        padding: var(--spacing-6);
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
        margin-bottom: var(--spacing-4);
    }
    
    /* Templates */
    .templates-preview {
        padding: var(--spacing-16) 0;
    }
    
    .templates-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
        margin-bottom: var(--spacing-8);
    }
    
    .template-preview {
        height: 150px;
    }
    
    .template-mockup {
        width: 100px;
        height: 130px;
    }
    
    /* Pricing */
    .pricing {
        padding: var(--spacing-16) 0;
    }
    
    .pricing-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
    }
    
    .pricing-card {
        padding: var(--spacing-6);
    }
    
    /* Footer */
    .footer {
        padding: var(--spacing-12) 0 var(--spacing-6);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
        text-align: center;
    }
    
    .footer-social {
        justify-content: center;
    }
    
    /* Modal */
    .modal-content {
        margin: 10% auto;
        padding: var(--spacing-6);
        width: 95%;
        max-width: 350px;
    }
    
    /* Utility classes for mobile */
    .mobile-hidden {
        display: none;
    }
    
    .mobile-visible {
        display: block;
    }
}

/* Extra small screens (480px and down) */
@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-3);
    }
    
    .nav-container {
        padding: 0 var(--spacing-3);
    }
    
    .hero-title {
        font-size: var(--font-size-2xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
    }
    
    .hero-mockup {
        width: 200px;
        height: 260px;
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .section-subtitle {
        font-size: var(--font-size-base);
    }
    
    .feature-card {
        padding: var(--spacing-4);
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .template-mockup {
        width: 80px;
        height: 110px;
    }
    
    .pricing-card {
        padding: var(--spacing-4);
    }
    
    .modal-content {
        padding: var(--spacing-4);
        margin: 15% auto;
    }
    
    .footer-content {
        gap: var(--spacing-4);
    }
}

/* Landscape orientation for mobile devices */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        padding: calc(80px + var(--spacing-8)) 0 var(--spacing-8);
    }
    
    .hero-container {
        gap: var(--spacing-8);
    }
    
    .hero-mockup {
        width: 200px;
        height: 260px;
    }
    
    .features,
    .templates-preview,
    .pricing {
        padding: var(--spacing-12) 0;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-mockup,
    .template-mockup {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print styles */
@media print {
    .header,
    .nav-toggle,
    .hero-buttons,
    .footer {
        display: none;
    }
    
    .hero {
        padding: var(--spacing-4) 0;
        min-height: auto;
    }
    
    .section-title,
    .hero-title {
        color: var(--gray-900) !important;
    }
    
    .feature-card,
    .template-card,
    .pricing-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
    
    a {
        text-decoration: underline;
    }
    
    .btn {
        border: 1px solid var(--gray-400);
        background: transparent !important;
        color: var(--gray-900) !important;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .hero-mockup::before {
        animation: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #0f172a;
        --gray-50: #1e293b;
        --gray-100: #334155;
        --gray-200: #475569;
        --gray-300: #64748b;
        --gray-400: #94a3b8;
        --gray-500: #cbd5e1;
        --gray-600: #e2e8f0;
        --gray-700: #f1f5f9;
        --gray-800: #f8fafc;
        --gray-900: #ffffff;
    }
    
    .header {
        background: var(--gray-50);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .hero {
        background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    }
    
    .features {
        background: var(--gray-50);
    }
    
    .templates-preview {
        background: var(--white);
    }
    
    .feature-card,
    .template-card,
    .pricing-card {
        background: var(--gray-50);
        border-color: var(--gray-200);
    }
    
    .template-mockup {
        background: var(--gray-100);
    }
    
    .mockup-header {
        background: var(--gray-300);
    }
    
    .mockup-line {
        background: var(--gray-400);
    }
    
    .modal-content {
        background: var(--gray-50);
    }
    
    .form-group input {
        background: var(--gray-100);
        border-color: var(--gray-300);
        color: var(--gray-700);
    }
    
    .form-group input:focus {
        background: var(--white);
    }
}

/* Focus styles for accessibility */
.btn:focus,
.nav-link:focus,
.social-link:focus,
.feature-link:focus,
input:focus,
button:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Skip to content link for accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: var(--radius);
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--gray-300);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error states */
.error {
    border-color: var(--error-color) !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.error-message {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-1);
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

/* Success states */
.success {
    border-color: var(--success-color) !important;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

.success-message {
    color: var(--success-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-1);
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}
